import DashboardLayout from "@/Layouts/DashBoardLayout";
import { renderForm } from "@/lib/functions/render-forms";
import { useRouter } from "next/router";
import React from "react";

const EditForm = () => {
  const router = useRouter();
  const dataType = router.query.dataType as string;

  if (!router.isReady) {
    return <div> LADEN...</div>;
  }

  return <div className="w-full">{renderForm(dataType)}</div>;
};

export default EditForm;

EditForm.getLayout = function getLayout(page: React.ReactElement) {
  return <DashboardLayout>{page}</DashboardLayout>;
};
