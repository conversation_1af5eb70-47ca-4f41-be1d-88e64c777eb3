import DashboardLayout from "@/Layouts/DashBoardLayout";
import { renderForm } from "@/lib/functions/render-forms";
import { useRouter } from "next/router";
import React from "react";

export default function CreateForm() {
  const router = useRouter();
  const dataType = router.query.dataType as string;

  // Show loading state while router is not ready
  if (!router.isReady) {
    return (
      <div className="flex items-center justify-center min-h-[400px]">
        <div className="text-center">
          <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-gray-900 mx-auto"></div>
          <p className="mt-2 text-muted-foreground">Laden...</p>
        </div>
      </div>
    );
  }

  return <div className="w-full">{renderForm(dataType)}</div>;
}

CreateForm.getLayout = function getLayout(page: React.ReactElement) {
  return <DashboardLayout>{page}</DashboardLayout>;
};
