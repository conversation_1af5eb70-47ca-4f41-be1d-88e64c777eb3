/* eslint-disable @typescript-eslint/no-explicit-any */
import { getTableDataFetchFuntion } from "@/lib/functions/table-data-loader";
import DashboardLayout from "@/Layouts/DashBoardLayout";
import { GetStaticPropsContext } from "next";
import { useRouter } from "next/router";
import React from "react";
import DashboardTablePage from "@/components/DashboardTablePage";
import { useSearch } from "@/lib/contexts/search-context";

const ContentPage = ({ data, head }: { data: any; head: any }) => {
  const router = useRouter();
  const dataType = router.query.dataType as string;
  const { searchTerm } = useSearch();

  return (
    <DashboardTablePage
      isLoading={!router.isReady}
      data={data}
      head={head}
      dataType={dataType}
      searchTerm={searchTerm}
    />
  );
};

export default ContentPage;

ContentPage.getLayout = function getLayout(page: React.ReactElement) {
  return <DashboardLayout>{page}</DashboardLayout>;
};

export async function getStaticPaths() {
  return {
    paths: [
      { params: { dataType: "users" } },
      { params: { dataType: "environments" } },
      { params: { dataType: "content-model" } },
      { params: { dataType: "sources" } },
    ],
    fallback: false,
  };
}

type Params = {
  dataType: string;
};

export async function getStaticProps(context: GetStaticPropsContext<Params>) {
  const dataType = context.params?.dataType;

  if (!dataType) {
    return {
      notFound: true,
    };
  }

  const { headers, loadFunction } = getTableDataFetchFuntion(dataType);

  const res = await loadFunction();
  if (!res) {
    return {
      notFound: true,
    };
  }

  return {
    props: {
      data: res,
      head: headers,
    },
  };
}
