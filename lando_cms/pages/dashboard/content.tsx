import DashboardTablePage from "@/components/DashboardTablePage";
import DashboardLayout from "@/Layouts/DashBoardLayout";
import { fetchContentData } from "@/lib/api/content-api";
import { useSearch } from "@/lib/contexts/search-context";
import { GetServerSidePropsContext } from "next";
import { useRouter } from "next/router";
import React from "react";

const Content = ({ data, head }: { data: unknown[]; head: string[] }) => {
  const router = useRouter();
  const { searchTerm } = useSearch();
  return (
    <div>
      <DashboardTablePage
        isLoading={!router.isReady}
        data={data}
        head={head}
        dataType={"content"}
        searchTerm={searchTerm}
      />
    </div>
  );
};

export default Content;

Content.getLayout = function getLayout(page: React.ReactElement) {
  return <DashboardLayout>{page}</DashboardLayout>;
};

export async function getServerSideProps(context: GetServerSidePropsContext) {
  const { req, res } = context;

  const response = await fetchContentData();
  const cookies = req.cookies;
  const authToken = cookies?.authToken;
  console.log(authToken);

  //wennn authToken nicht valide ist auslogen

  if (authToken === "12345678") {
    res.statusCode = 401;
    const result = {
      redirect: {
        destination: "/",
        permanent: false,
      },
    };
    return result;
  }

  return {
    props: {
      data: response,
      head: ["Title", "Rating", "Created At", "Created By", "Status"],
    },
  };
}
