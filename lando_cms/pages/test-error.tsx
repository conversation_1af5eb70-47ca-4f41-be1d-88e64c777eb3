function BreakingComponent() {
  // Eine Bedingung, die einen Fehler auslöst
  if (
    typeof window !== "undefined" &&
    window.location.pathname === "/test-error"
  ) {
    // Dieser Fehler wird während des Renderings ausgelöst
    throw new Error(
      "Dies ist ein absichtlicher Fehler für die Error Boundary."
    );
  }
  return <p>Diese Komponente ist normalerweise in Ordnung.</p>;
}

export default function TestErrorPage() {
  return (
    <div className="flex flex-col items-center justify-center min-h-screen bg-blue-50 text-blue-800">
      <h1 className="text-2xl font-bold mb-4">Testseite für Error Boundary</h1>
      <p className="mb-8">Der Fehler sollte gleich auftreten...</p>
      <BreakingComponent /> {/* Die Komponente, die den Fehler auslöst */}
    </div>
  );
}
