import Image from "next/image";
import { <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>_Mono } from "next/font/google";
import LoginForm from "@/components/LoginForm";

const geistSans = Geist({
  variable: "--font-geist-sans",
  subsets: ["latin"],
});

const geistMono = Geist_Mono({
  variable: "--font-geist-mono",
  subsets: ["latin"],
});

export default function Home() {
  return (
    <div
      className={`${geistSans.className} ${geistMono.className} grid grid-rows-[50%_1fr] w-full h-full  justify-items-center `}
    >
      <div className="w-1/2 mt-12">
        <Image width={400} height={400} src={"/main_dark.png"} alt="Logo" />
      </div>
      <div className={"w-1/2 flex flex-col min-w-[400px] shadow-sm p-8 gap-4"}>
        <h3>Loggin</h3>
        <p>Please enter your email to login</p>
        <LoginForm />
      </div>
    </div>
  );
}
