import ErrorBoundary from "@/components/ErrorBoundary";
import { AppProvider } from "@/components/wrapper/AppProvider";
import "@/styles/globals.css";
import { NextPage } from "next";
import type { AppProps } from "next/app";
import { useRouter } from "next/router";
import { ReactElement, ReactNode } from "react";

type NextPageWithLayout = NextPage & {
  getLayout?: (page: ReactElement) => ReactNode;
};

type AppPropsWithLayout = AppProps & {
  Component: NextPageWithLayout;
};

export default function App({ Component, pageProps }: AppPropsWithLayout) {
  const getLayout = Component.getLayout || ((page) => page);
  const router = useRouter();

  return (
    <AppProvider>
      {getLayout(
        <ErrorBoundary router={router}>
          <Component {...pageProps} />
        </ErrorBoundary>
      )}
    </AppProvider>
  );
}
