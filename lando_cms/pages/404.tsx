import DefaultButton, { ButtonType } from "@/components/ui/DefaultButton";
import { useRouter } from "next/router";

const NotFound = () => {
  const route = useRouter();
  return (
    <div className="w-screen h-screen flex justify-center items-center flex-col gap-y-4">
      <h1 className="text-5xl">Hoopla! Die Seite existiert nicht</h1>
      <DefaultButton
        text="Zurück zum Dashboard"
        type={ButtonType.PRIMARY}
        onClick={() => route.push("/dashboard/content")}
      />
    </div>
  );
};

export default NotFound;
