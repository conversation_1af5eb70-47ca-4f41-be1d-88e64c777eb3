{"name": "lando_cms", "version": "0.1.0", "private": true, "scripts": {"dev": "next dev", "build": "next build", "start": "next start", "lint": "next lint", "build:no-lint": "DISABLE_ESLINT_PLUGIN=true next build"}, "dependencies": {"@emotion/react": "^11.14.0", "@emotion/styled": "^11.14.0", "@mui/material": "^7.1.1", "@mui/x-date-pickers": "^8.5.1", "@tanstack/react-query": "^5.80.6", "@types/bcryptjs": "^2.4.6", "bcryptjs": "^3.0.2", "cookie": "^1.0.2", "dayjs": "^1.11.13", "framer-motion": "^12.17.3", "js-cookie": "^3.0.5", "js-md5": "^0.8.3", "lucide-react": "^0.514.0", "next": "15.3.3", "next-auth": "^5.0.0-beta.28", "react": "^19.0.0", "react-dom": "^19.0.0", "react-hook-form": "^7.58.1", "react-icons": "^5.5.0", "zod": "^3.25.67"}, "devDependencies": {"@eslint/eslintrc": "^3", "@tailwindcss/postcss": "^4", "@types/js-cookie": "^3.0.6", "@types/node": "^20", "@types/react": "^19", "@types/react-dom": "^19", "eslint": "^9", "eslint-config-next": "15.3.3", "eslint-config-prettier": "^10.1.5", "tailwindcss": "^4", "typescript": "^5"}}