import { SidebarNav } from "../components/Sidebar";
import {
  FaAward,
  FaBuilding,
  FaChevronLeft,
  FaFile,
  FaFileExport,
  FaHome,
  FaPaperPlane,
  FaSourcetree,
  FaUser,
} from "react-icons/fa";
import { FaGear } from "react-icons/fa6";
import { Searchbar } from "../components/ui/Searchbar";
import { Profile } from "../components/ProfileComponent";
import { EnvironmentProvider } from "@/lib/contexts/environment-context";
import UserProvider from "@/lib/contexts/user-context";
import { GiProcessor } from "react-icons/gi";
import { SearchProvider } from "@/lib/contexts/search-context";
export default function DashboardLayout({
  children,
}: {
  children: React.ReactElement;
}) {
  const menuItems = [
    {
      href: "/dashboard/home",
      icon: <FaHome />,
      label: "Home",
    },
    {
      href: "/dashboard/users",
      icon: <FaUser />,
      label: "Users",
    },
    {
      href: "/dashboard/enterprise",
      icon: <FaBuilding />,
      label: "Enterprise",
    },
    {
      href: "/dashboard/release-process",
      icon: <GiProcessor />,
      label: "Release-Process",
    },
    {
      href: "/dashboard/environments",
      icon: <FaAward />,
      label: "Environments",
    },
    {
      href: "/dashboard/content-model",
      icon: <FaFile />,
      label: "Content Model",
    },
    {
      href: "/dashboard/content",
      icon: <FaFile />,
      label: "Content",
    },
    {
      href: "/dashboard/sources",
      icon: <FaSourcetree />,
      label: "Sources",
    },
    {
      href: "/dashboard/export-area",
      icon: <FaFileExport />,
      label: "ExportArea",
    },
    {
      href: "/dashboard/publish-area",
      icon: <FaPaperPlane />,
      label: "Publish Area",
    },
    {
      href: "/dashboard/regulatory-affairs",
      icon: <FaAward />,
      label: "Regulatory Affairs",
    },
    {
      href: "/settings",
      icon: <FaGear />,
      label: "Settings",
    },
  ];

  return (
    <div className={"h-screen grid grid-cols-[300px_1fr] "}>
      <nav className={"border-r border-gray-200 bg-[#081A20]"}>
        <div>
          <div
            className={"flex justify-around items-center  h-[80px] p-[10px]"}
          >
            <h1 className={"text-[#339988] text-3xl font-black p-2 rounded-md"}>
              Lando <span className="text-white">CMS</span>
            </h1>
            <span>
              <FaChevronLeft />
            </span>
          </div>
          <SidebarNav menuItems={menuItems} />
        </div>
      </nav>
      <UserProvider>
        <EnvironmentProvider>
          <SearchProvider>
            <main className="bg-gray-50">
              <header className="flex justify-between bg-white items-center shadow-lg h-[80px] px-8  ">
                <Searchbar />
                <Profile />
              </header>

              {children}
            </main>
          </SearchProvider>
        </EnvironmentProvider>
      </UserProvider>
    </div>
  );
}
