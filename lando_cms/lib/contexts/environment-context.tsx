"use client";
import { useQuery } from "@tanstack/react-query";
import { createContext, useCallback, useState } from "react";
import { ENVIRONMENT_QUERY_KEY, fetchEnvironments } from "../api/content-api";
import IEnvironment from "../types/IEnvironment";

interface IEnvironmentContext {
  environment: string;
  mutateEnvironment: (environment: string) => void;
  environments: IEnvironment[] | null;
}

export const EnvironmentContext = createContext<IEnvironmentContext>({
  environment: "1",
  mutateEnvironment: () => {},
  environments: null,
});

export const EnvironmentProvider = ({
  children,
}: {
  children: React.ReactNode;
}) => {
  const [environment, setEnvironment] = useState<string>("1");

  const { data: environments } = useQuery({
    queryKey: [ENVIRONMENT_QUERY_KEY],
    queryFn: fetchEnvironments,
    staleTime: 5 * 60 * 1000,
  });

  const mutateEnvironment = useCallback((environment: string) => {
    setEnvironment(environment);
  }, []);

  return (
    <EnvironmentContext.Provider
      value={{ environment, mutateEnvironment, environments }}
    >
      {children}
    </EnvironmentContext.Provider>
  );
};
