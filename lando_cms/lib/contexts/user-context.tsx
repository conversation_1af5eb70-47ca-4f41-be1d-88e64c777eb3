import React, { createContext, useEffect, useState } from "react";
import { queryClient } from "../api/api-client";
import { USER_QUERY_KEY } from "../api/user-api";
import { IUser } from "../types/IUser";

const UserContext = createContext<IUser | null>(null);

interface UserProviderProps {
  children: React.ReactNode;
}

export default function UserProvider({ children }: UserProviderProps) {
  const [userData, setUserData] = useState<IUser | null>(null);

  useEffect(() => {
    const user = queryClient.getQueryData([USER_QUERY_KEY]);
    console.log(user);

    if (user) {
      setUserData(user as IUser);
    }
  }, []);

  return (
    <UserContext.Provider value={userData}>{children}</UserContext.Provider>
  );
}
