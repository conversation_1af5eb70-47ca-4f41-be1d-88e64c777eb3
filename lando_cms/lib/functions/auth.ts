import bcrypt from "bcryptjs";

const SALT_ROUNDS = 10;

export async function saltAndHashPassword(
  password: string | unknown
): Promise<string> {
  if (!password || typeof password !== "string") {
    throw new Error("Das Passwort muss ein gültiger String sein.");
  }

  try {
    const hashedPassword = await bcrypt.hash(password, SALT_ROUNDS);
    return hashedPassword;
  } catch (error) {
    console.error("Fehler beim Hashing des Passworts:", error);
    throw new Error("Passwort-Hashing fehlgeschlagen.");
  }
}

export async function verifyPassword(
  password: string,
  hashedPassword: string
): Promise<boolean> {
  if (!password || typeof password !== "string") {
    throw new Error("Das eingegebene Passwort muss ein gültiger String sein.");
  }
  if (!hashedPassword || typeof hashedPassword !== "string") {
    throw new Error(
      "Das Hash-Passwort aus der Datenbank muss ein gültiger String sein."
    );
  }

  try {
    const isValid = await bcrypt.compare(password, hashedPassword);
    return isValid;
  } catch (error) {
    console.error("Fehler beim Vergleichen der Passwörter:", error);
    // Im Fehlerfall zur Sicherheit 'false' zurückgeben oder den Fehler erneut werfen
    throw new Error("Passwort-Vergleich fehlgeschlagen.");
  }
}
