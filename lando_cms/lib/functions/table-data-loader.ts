import { fetchContentData } from "../api/content-api";
import fetchContentModelData from "../api/contentmodel-api";
import fetchEnterpriseData from "../api/enterprise-api";
import fetchEnvironments from "../api/enviroment-api";
import fetchExportAreaDatas from "../api/exportarea-client";
import fetchPublishAreaData from "../api/publisharea-api";
import fetchRegulatoryAffairs from "../api/regulatory-api";
import fetchSources from "../api/sources-api";
import { fetchAllUsers } from "../api/users-api";

type LoadFunctionParameter = {
  environment: string;
  user_id: string;
};

export const getTableDataFetchFuntion = (dataType: string) => {
  switch (dataType) {
    case "content":
      return {
        headers: ["Title", "Rating", "Created At", "Created By", "Status"],
        loadFunction: fetchContentData,
      };
    case "users":
      return {
        headers: [
          "Vorname",
          "Nachname",
          "Gruppe",
          "Position",
          "Email",
          "Status",
        ],
        loadFunction: fetchAllUsers,
      };
    case "environments":
      return {
        headers: [
          "Environment Titel",
          "Erstellt Am",
          "Erstellt Von",
          "Grösse",
          "Status",
        ],
        loadFunction: fetchEnvironments,
      };
    case "export-area":
      return {
        headers: [
          "Exportiert Von",
          "Exportiert Nach",
          "Exportiert Bei",
          "Export Ende",
          "Sprache",
        ],
        loadFunction: async (params?: LoadFunctionParameter) =>
          fetchExportAreaDatas(params ? params.environment : ""),
      };
    case "publish-area":
      return {
        headers: [
          "Content Name",
          "Zuletzt Bearbeitet am",
          "Initialisiert Von",
          "Prozess Id",
          "Prozess Info",
          "Auswählen",
        ],
        loadFunction: fetchPublishAreaData,
      };
    case "content-model":
      return {
        headers: [
          "Content Model Name",
          "Ard des Content Model",
          "Erstellt Von",
          "Erstellt Am",
          "Status",
        ],
        loadFunction: fetchContentModelData,
      };
    case "sources":
      return {
        headers: [
          "Datei Typ",
          "Titel",
          "Erstellt Von",
          "Zuletzt Aktualisiert Am",
          "Ablaufdatum",
        ],
        loadFunction: fetchSources,
      };
    case "enterprise":
      return {
        headers: [
          "Kundenname",
          "Kundenfarbe",
          "Ansprechpartner",
          "CMS LINK",
          "Status",
        ],
        loadFunction: fetchEnterpriseData,
      };
    case "regulatory-affairs":
      return {
        headers: [
          "Titel",
          "Prozess ID",
          "Initialisiert Am",
          "Initialisert Von",
          "Global Status",
        ],
        loadFunction: fetchRegulatoryAffairs,
      };
    case "cluster":
      return {
        headers: ["Title", "Rating", "Created At", "Created By", "Status"],
        loadFunction: () => {
          fetchContentData();
        },
      };
    default:
      return {
        headers: ["Title", "Rating", "Created At", "Created By", "Status"],
        loadFunction: () => {
          fetchContentData();
        },
      };
  }
};
