import { ContentModelForm } from "@/components/Forms/ContentModelForm";
import { EnterpriseForm } from "@/components/Forms/EnterpriseForm";
import { EnvironmentForm } from "@/components/Forms/EnvironmentForm";
import { ExportForm } from "@/components/Forms/ExportForm";
import { SourceForm } from "@/components/Forms/SourceForm";
import { UserForm } from "@/components/Forms/UserForm";
import {
  Card,
  CardContent,
  CardDescription,
  CardHeader,
  CardTitle,
} from "@/components/ui/Card";

export const renderForm = (dataType: string) => {
  switch (dataType) {
    case "users":
      return <UserForm />;
    case "enterprise":
      return <EnterpriseForm />;
    case "environments":
      return <EnvironmentForm />;
    case "sources":
      return <SourceForm />;
    case "export-area":
      return <ExportForm />;
    case "content-model":
      return <ContentModelForm />;
    default:
      return <DefaultForm />;
  }
};

function DefaultForm() {
  return (
    <Card>
      <CardHeader>
        <CardTitle>Formular auswählen</CardTitle>
        <CardDescription>
          Bitte wählen Sie einen gültigen Datentyp
        </CardDescription>
      </CardHeader>
      <CardContent className="space-y-4">
        <p className="text-muted-foreground">Verfügbare Datentypen:</p>
        <ul className="list-disc list-inside space-y-1 text-sm text-muted-foreground">
          <li>user - Benutzer erstellen</li>
          <li>product - Produkt erstellen</li>
          <li>article - Artikel erstellen</li>
        </ul>
        <p className="text-sm text-muted-foreground">
          Beispiel:{" "}
          <code className="bg-muted px-1 py-0.5 rounded">?dataType=user</code>
        </p>
      </CardContent>
    </Card>
  );
}
