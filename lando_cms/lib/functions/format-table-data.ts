/* eslint-disable @typescript-eslint/no-explicit-any */
import { IContent } from "../types/IContent";
import IEnvironment from "../types/IEnvironment";
import ISource from "../types/ISources";
import IExportData from "../types/IExportArea";
import IPublishArea from "../types/IPublishArea";
import IEnterprise from "../types/IEnterprise";
import IContentModel from "../types/IContentModel";
import { IUser } from "../types/IUser";

export default function formatTableData(data: any[], dataType: string): any[] {
  switch (dataType) {
    case "content":
      return formatContent(data as IContent[]);
    case "environments":
      return formatEnvironment(data as IEnvironment[]);
    case "sources":
      return formatSources(data as ISource[]);
    case "export-area":
      return formatExportAreaData(data as IExportData[]);
    case "users":
      return formatUsers(data as IUser[]);
    case "publish-area":
      return formatPublishArea(data as IPublishArea[]);
    case "enterprise":
      return formatEnterpriseData(data as IEnterprise[]);
    case "content-model":
      return formatContentModelData(data as IContentModel[]);
    default:
      return [];
  }
}

function formatContent(content: IContent[]) {
  return content?.map((c: IContent) => {
    return {
      title: c?.title,
      rating: c?.rating,
      createdAt: c?.created_at,
      createdBy: c?.created_by,
      status: c?.status,
    };
  });
}

function formatEnvironment(environment: IEnvironment[]) {
  console.log(environment);
  return environment?.map((e: IEnvironment) => {
    return {
      environmentName: e?.environment_name,
      createdAt: e?.created_at,
      createdBy: e?.created_by,
      fileSize: e?.file_size,
      status: e?.status,
    };
  });
}

function formatSources(sources: ISource[]) {
  return sources?.map((source: ISource) => {
    return {
      fileType: source?.document_type,
      title: source.title,
      createdBy: source?.created_by,
      updatedAt: source?.history.modified_at,
      reminderDate: source.reminder_date,
    };
  });
}

function formatExportAreaData(exportAreaDatas: IExportData[]) {
  return exportAreaDatas?.map((exportArea: IExportData) => {
    return {
      exportedFrom: exportArea?.origin_collection_name,
      exportedTo: exportArea?.exported_to,
      exportedBy: exportArea?.meta_data?.exported_by,
      exportEnd: exportArea?.meta_data?.end_time,
      exportLanguage: exportArea?.export_language,
    };
  });
}

function formatUsers(users: IUser[]) {
  return users?.map((user: IUser) => {
    return {
      firstName: user?.first_name,
      lastName: user?.last_name,
      department: user?.department,
      email: user?.email,
    };
  });
}

function formatPublishArea(publishAreaDatas: IPublishArea[]) {
  return publishAreaDatas?.map((publishArea: IPublishArea) => {
    return {
      contentName: publishArea?.initialization?.content_id,
      lastModifiedAt: "hiert datum",
      initializedBy: publishArea?.initialization?.initialized_by,
      process: publishArea?.process_id,
      processInfo: publishArea?.initialization?.process_type,
    };
  });
}

function formatEnterpriseData(enterpriseData: IEnterprise[]) {
  return enterpriseData?.map((enterprise: IEnterprise) => {
    return {
      customerName: enterprise?.customer_name,
      customerColor:
        enterprise?.colors?.accent_dark +
        " & " +
        enterprise?.colors?.accent_light,
      contact: enterprise?.contact_person?.name,
      cmsLink: enterprise?.cms_link,
      status: enterprise?.status,
    };
  });
}

function formatContentModelData(contentModelData: IContentModel[]) {
  return contentModelData?.map((contentModel: IContentModel) => {
    return {
      contentModelName: contentModel?.title,
      ContentType: contentModel?.content_type_id,
      CreatedBy: contentModel?.created_by,
      createdAt: contentModel?.created_at,
      status: contentModel?.status,
    };
  });
}
