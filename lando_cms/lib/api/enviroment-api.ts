import { ApiClient } from "./api-client";

export default async function fetchEnvironments() {
  const user_id = "6567384f028aacd1429bd428";

  const environments = await ApiClient.SendApiRequest(
    "/env?language=en&user_id=" + user_id + "&load_type=" + "hard",
    "GET"
  );
  let combinedEnv = [];
  if (
    environments?.active_environments?.length > 0 &&
    environments?.inactive_environments?.length > 0
  ) {
    combinedEnv = environments?.active_environments?.concat(
      environments.inactive_environments
    );
  } else if (environments?.environments) {
    combinedEnv = environments?.environments;
  } else {
    combinedEnv =
      environments?.active_environments || environments?.inactive_environments;
  }

  return combinedEnv;
}
