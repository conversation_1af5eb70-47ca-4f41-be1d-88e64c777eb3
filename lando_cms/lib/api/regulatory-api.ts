import { ApiClient } from "./api-client";

export default async function fetchRegulatoryAffairs() {
  const user_id = "6567384f028aacd1429bd428";

  const res = await ApiClient.SendApiRequest(
    "/process/load_process_data/regulatory_affairs?language=de",
    "POST",
    {
      loadedBy: user_id,
      userFilter: [user_id],
      processTypeFilter: ["normalResultPage"],
      statusFilter: ["initialized"],
    }
  );

  console.log(res);
}
