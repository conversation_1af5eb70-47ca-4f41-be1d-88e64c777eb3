import { ApiClient } from "./api-client";

export const SOURCES_QUERY_KEY = "sources";

export default async function fetchSources() {
  const user_id = "6567384f028aacd1429bd428";

  const allSources = await ApiClient.SendApiRequest(
    "/source?user_id=" +
      user_id +
      "&language=en&skip=0&limit=1000&load_type=hard",
    "GET"
  );

  let combinedSources = [];
  if (
    allSources?.active_sources?.length > 0 &&
    allSources?.inactive_sources?.length > 0
  ) {
    combinedSources = allSources?.active_sources?.concat(
      allSources.inactive_sources
    );
  } else {
    combinedSources =
      allSources?.active_sources || allSources?.inactive_sources;
  }

  console.log(combinedSources);

  return combinedSources;
}
