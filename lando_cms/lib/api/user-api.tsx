import Cookies from "js-cookie";

import { queryClient } from "./api-client";
import { IUser } from "../types/IUser";

export const USER_QUERY_KEY = "user";

export const loginUser = async (email: string, password: string) => {
  const response = await fetch(
    "https://cms-v2-cms-v2-backend.q9gv7g.easypanel.host/api/v1/users/login",
    {
      method: "POST",
      headers: {
        "Content-Type": "application/json",
        accept: "application/json",
      },
      body: JSON.stringify({
        email,
        password,
      }),
    }
  ).then((res) => res.json());

  if (response.error) {
    throw new Error(response.error);
  }

  if (response.token) {
    Cookies.set("token", response.token.access_token);
  }

  console.log(response.user);

  return response.user;
};

export const loadMe = async () => {
  const response = await fetch(
    "https://cms-v2-cms-v2-backend.q9gv7g.easypanel.host/api/v1/users/me",
    {
      headers: {
        Authorization: `Bearer ${Cookies.get("token")}`,
      },
    }
  ).then((res) => res.json());

  if (response.error) {
    throw new Error(response.error);
  }

  return response.user;
};

export const getToken = () => {
  return Cookies.get("token");
};

export const getUserId = () => {
  const user = queryClient.getQueryData([USER_QUERY_KEY]) as IUser;
  return user?._id;
};

export const getCurrentUser = () => {
  const user = queryClient.getQueryData([USER_QUERY_KEY]) as IUser;
  return user;
};
