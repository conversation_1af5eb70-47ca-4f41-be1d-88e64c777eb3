import { QueryClient } from "@tanstack/react-query";

export const queryClient = new QueryClient({
  defaultOptions: {
    queries: {
      staleTime: 1000 * 60 * 1,
      gcTime: 1000 * 60 * 10,
      refetchOnWindowFocus: false,
    },
  },
});

export class ApiClient {
  private static BASE_URL = "https://dev.api.aristophania.mediceo.com";

  static async SendApiRequest(path: string, method: string, body?: object) {
    const params = {
      method: method,
      headers: {
        "Content-Type": "application/json",
      },
    };

    if (body) {
      Object.assign(params, { body: JSON.stringify(body) });
    }

    const response = await fetch(`${this.BASE_URL}${path}`, params);

    if (!response.ok) {
      throw new Error("Failed to fetch data");
    }

    return response.json();
  }
}
