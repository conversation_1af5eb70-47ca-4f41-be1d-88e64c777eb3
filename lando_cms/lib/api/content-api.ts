import { ApiClient, queryClient } from "./api-client";

export const CONTENT_QUERY_KEY = "content";
export const ENVIRONMENT_QUERY_KEY = "environments";

export const fetchEnvironments = async () => {
  //const user: any = queryClient.getQueryData(["user"]);

  const environments = await ApiClient.SendApiRequest(
    "/env?language=en&user_id=" +
      "6567384f028aacd1429bd428" +
      "&load_type=soft",
    "GET"
  );
  return environments.environments;
};

export interface ContentQueryParam {
  environment: string;
  user: unknown;
  skip: number;
  limit: number;
  filter: string;
  active_only: boolean;
  sortBy: string;
  sortOrder: string;
  publishing_status: string;
  from_date: string;
  until_date: string;
  level: string[];
}

export const fetchContentData = async () => {
  const slug =
    "/env/content/65c366892ab53918deb4377c?language=en&user_id=6567384f028aacd1429bd428&skip=0&limit=1000&active_only=true&sort_by=last_modified_at&sort_order=descending";

  /*let slug2 =
    "/env/content/" +
    environment +
    "?language=en&user_id=" +
    user._id +
    `&skip=${skip}&limit=${limit}${
      filter != "alle" ? "&content_type_filter=" + filter : ""
    }&active_only=${active_only}`;
  if (sortBy) slug2 = slug2 + "&sort_by=" + sortBy;
  if (sortOrder) slug2 = slug2 + "&sort_order=" + sortOrder;
  if (publishing_status)
    slug2 = slug2 + "&publishing_status=" + publishing_status;
  if (from_date) slug2 = slug2 + "&from_date=" + from_date;
  if (until_date) slug2 = slug2 + "&until_date=" + until_date;
  if (level?.length > 0) {
    for (let i = 0; i < level?.length; i++)
      slug2 = slug2 + "&level_filter=" + level[i];
  }*/

  const res = await ApiClient.SendApiRequest(slug, "GET");
  console.log("CONTENT", res);
  return res?.data?.content;
};

export const getContentData = () => {
  const content = queryClient.getQueryData([CONTENT_QUERY_KEY]);
  return content;
};
