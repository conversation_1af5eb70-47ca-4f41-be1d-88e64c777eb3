import { queryClient } from "./api-client";
import { getToken } from "./user-api";

export const USERS_QUERY_KEY = "users";

export const fetchAllUsers = async () => {
  const users = await fetch(
    "https://cms-v2-cms-v2-backend.q9gv7g.easypanel.host/api/v1/users/?user_type=admin&skip=0&limit=100",
    {
      headers: {
        "Content-Type": "application/json",
        Authorization: `Bearer ${getToken()}`,
      },
    }
  ).then((res) => res.json());

  console.log(users);

  if (users?.users) return users.users;
  return [];
};

export const getAllUsers = () => {
  return queryClient.getQueryData([USERS_QUERY_KEY]);
};
