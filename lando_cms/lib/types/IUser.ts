export interface IUser {
  email: string;
  first_name: string;
  last_name: string;
  department: string;
  user_type: "admin" | "user" | "guest"; // Annahme: user_type könnte auch andere Werte haben
  meta_data: MetaData;
  settings: UserSettings;
  preferences: UserPreferences;
  _id: string; // MongoDB ObjectId
}

export interface MetaData {
  created_at: string; // ISO 8601 string
  updated_at: string; // ISO 8601 string
  created_by: string; // MongoDB ObjectId string
  is_active: boolean;
  updated_initial_password: boolean;
  last_login: string; // ISO 8601 string
  last_password_change: string; // ISO 8601 string
}

export interface UserSettings {
  language: string; // z.B. 'de', 'en'
  timezone: string; // z.B. 'Europe/Berlin'
  theme: "light" | "dark"; // Annahme: könnte auch andere Werte haben
}

export interface UserPreferences {
  home_screen: HomeScreenPreferences;
}

export interface HomeScreenPreferences {
  sections: Section[];
}

export interface Section {
  title: string;
  index: number;
  items: HomeMenuItem[];
}

export interface HomeMenuItem {
  title: string;
  description: string;
  icon: string; // z.B. URL zum Icon oder Icon-Name
  url: string; // z.B. Pfad oder URL
}
