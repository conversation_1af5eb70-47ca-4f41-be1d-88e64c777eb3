interface ContentHistoryItem {
  modified_by: string;
  modified_at: string; // ISO 8601 string
  change_type: string; // e.g., "update"
  // Add any other properties if they exist for the 'Object' entries
}

interface ContentStructureItem {
  // Define properties if the structure of these objects is known.
  // For now, it's left as a generic object since details aren't provided.
  [key: string]: unknown;
}

export default interface IContentModel {
  _id: string;
  accessible_by: string[];
  comments: unknown[]; // Assuming comments can be of any type if structure isn't known
  content_type_id: string;
  created_at: string; // ISO 8601 string
  created_by: string;
  description: string;
  history: ContentHistoryItem[];
  status: string;
  structure: ContentStructureItem[];
  tags: string[]; // Assuming tags are strings
  title: string;
  viewable_by: string[];
}
