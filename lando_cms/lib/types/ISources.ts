export default interface ISource {
  _id: string;
  attached_files: unknown;
  author: string;
  copyright: string[];
  created_by: string;
  document_type: string;
  history: SourceHistory;
  item_type: string;
  links: {
    linkid: string;
    link_title: string;
    link: string;
    status: string;
    last_checked: string;
  };
  notes: string;
  publishing_year: number;
  reminder_date: string;
  source_language: string;
  title: string;
  updated_by: string;
}

interface SourceHistory {
  modified_by: string;
  modified_at: string;
  change_type: string;
  changes: string[];
}
