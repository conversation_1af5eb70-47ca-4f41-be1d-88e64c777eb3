interface ExportedContentSummaryItem {
  id: string;
  title: string;
}

interface MetaData {
  exported_by: string;
  start_time: string; // ISO 8601 string
  end_time: string; // ISO 8601 string
  comment: string;
  exported_documents: number;
}

interface Filter {
  rating_filter: number[];
  publishing_status_filter: string[];
}

interface Status {
  process_running: boolean;
  current_state: string;
}

export default interface IExportData {
  _id: string;
  customer_id: string | null;
  export_details: unknown[];
  export_language: string;
  export_type: string;
  exported_content_summary: ExportedContentSummaryItem[];
  exported_to: string;
  filter: Filter;
  is_customer: boolean;
  meta_data: MetaData;
  origin_collection_name: string;
  status: Status;
}
