interface Archive {
  archive_status: boolean;
  archive_timestamp: string | null; // ISO 8601 string oder null
  archive_reason: string | null;
  archived_by: string | null;
}

interface Initialization {
  environment_id: string;
  content_id: string;
  initialized_by: string;
  notes: string;
  process_type: string;
  // Fügen Sie hier weitere mögliche Felder hinzu, falls vorhanden
}

interface Review {
  id: string;
}

interface MedicalDepartmentReview {
  required_reviews: number;
  reviews: Review[];
}

interface MedicalSupervisorReview {
  required_reviews: number;
  reviews: Review[];
}

export default interface IPublishArea {
  _id: string;
  archive: Archive;
  global_end_timestamp: string; // ISO 8601 string
  global_start_timestamp: string; // ISO 8601 string
  global_status: string;
  initialization: Initialization;
  medical_department_review: MedicalDepartmentReview;
  medical_department_reviews_finished: number | null;
  medical_supervisor_review: MedicalSupervisorReview;
  medical_supervisor_reviews_finished: number | null;
  process_id: string;
}
