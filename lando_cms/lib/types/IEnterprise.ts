export default interface IEnterprise {
  _id: string;
  cms_link: string;
  colors: {
    accent_light: string;
    accent_dark: string;
  };
  contact_person: {
    name: string;
    email: string;
  };
  created_by: string;
  customer_name: string;
  default_language: string;
  internal_customer_id: string;
  logo: {
    file_name: string;
    file_ref: string; // URL
    file_id: string;
    show_logo: boolean;
  };
  managed_by: string[]; // Array von IDs
  meta_data: {
    created_by: string;
    created_at: string; // ISO 8601 string
    last_modified_by: string;
    last_modified_at: string; // ISO 8601 string
  };
  status: string;
}
