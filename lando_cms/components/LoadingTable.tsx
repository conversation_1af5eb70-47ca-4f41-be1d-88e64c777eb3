export function LoadingTable() {
  return (
    <table className="bg-white w-full border border-gray-300 rounded-lg">
      <thead>
        <tr>
          <th className="p-2 border-b-2 border-gray-300">
            <div className="bg-gray-100 rounded-lg p-1"></div>
          </th>
          <th className="p-2 border-b-2 border-gray-300">
            <div className="bg-gray-100 rounded-lg p-1"></div>
          </th>
          <th className="p-2 border-b-2 border-gray-300">
            <div className="bg-gray-100 rounded-lg p-1"></div>
          </th>
          <th className="p-2 border-b-2 border-gray-300">
            <div className="bg-gray-100 rounded-lg p-1"></div>
          </th>
        </tr>
      </thead>
      <tbody>
        {Array.from({ length: 8 }).map((_, index) => (
          <tr key={index} className="border-b border-gray-300">
            <td className="p-6 border-b border-gray-300">
              <div className="h-4 rounded-md w-full animate-pulse-loader"></div>
            </td>
            <td className="p-6 border-b border-gray-300">
              <div className="h-4 rounded-md w-full animate-pulse-loader"></div>
            </td>
            <td className="p-6 border-b border-gray-300">
              <div className="h-4 rounded-md w-full animate-pulse-loader"></div>
            </td>
            <td className="p-6 border-b border-gray-300">
              <div className="h-4 rounded-md w-full animate-pulse-loader"></div>
            </td>
          </tr>
        ))}
      </tbody>
    </table>
  );
}
