import { EnvironmentContext } from "@/lib/contexts/environment-context";
import { memo, useContext } from "react";
import IEnvironment from "@/lib/types/IEnvironment";

const EnvironmentSelector = () => {
  const { environment, mutateEnvironment, environments } =
    useContext(EnvironmentContext);

  return (
    <div style={{ width: "150px" }}>
      <select
        value={environment}
        onChange={(e) => {
          mutateEnvironment(e.target.value);
        }}
        className={
          "appearance-none w-full rounded-lg border-none p-3 text-base font-normal bg-amber-300 mr-[30px] hover:cursor-pointer hover:scale-105 hover:transition hover:duration-100 hover:ease-in-out focus:outline-none active:outline-none active:border-3 active:border-[#339988]"
        }
      >
        {environments?.map((environment: IEnvironment) => (
          <option
            key={environment._id + environment.environment_name}
            value={environment._id}
          >
            {environment.environment_name}
          </option>
        ))}
      </select>
    </div>
  );
};

export default memo(EnvironmentSelector);
