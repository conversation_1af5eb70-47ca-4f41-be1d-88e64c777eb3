import { redirect } from "next/navigation";
import { NextRouter } from "next/router";
import { Component, ErrorInfo, ReactNode } from "react";

interface ErrorBoundaryProps {
  children: ReactNode;
  fallback?: ReactNode; // Optional: Eine Fallback-UI, die angezeigt wird
  router?: NextRouter;
}

interface ErrorBoundaryState {
  hasError: boolean;
  error: Error | null;
  errorInfo: ErrorInfo | null;
}

class ErrorBoundary extends Component<ErrorBoundaryProps, ErrorBoundaryState> {
  constructor(props: ErrorBoundaryProps) {
    super(props);
    this.state = { hasError: false, error: null, errorInfo: null };
  }

  static getDerivedStateFromError(error: Error): ErrorBoundaryState {
    return { hasError: true, error: error, errorInfo: null };
  }

  componentDidCatch(error: Error, errorInfo: ErrorInfo): void {
    console.error("Uncaught error in ErrorBoundary:", error, errorInfo);

    this.setState({
      error: error,
      errorInfo: errorInfo,
    });
  }
  render(): ReactNode {
    if (this.state.hasError) {
      // Du kannst eine benutzerdefinierte Fallback-UI rendern
      if (this.props.fallback) {
        return this.props.fallback;
      }

      return (
        <div className="flex flex-col items-center justify-center min-h-screen bg-red-100 text-red-800 p-4">
          <h1 className="text-3xl font-bold mb-4">
            Upps! Da ist etwas schiefgelaufen.
          </h1>
          <p className="text-lg text-center mb-6">
            Wir entschuldigen uns für die Unannehmlichkeiten. Bitte versuchen
            Sie es später noch einmal.
          </p>
          {/* Optional: Details für den Debug-Modus anzeigen */}
          {process.env.NODE_ENV === "development" && this.state.error && (
            <details className="w-full max-w-md bg-white p-4 rounded-md shadow-md text-gray-800 text-left">
              <summary className="font-semibold cursor-pointer">
                Fehlerdetails anzeigen
              </summary>
              <pre className="mt-2 text-sm whitespace-pre-wrap break-words">
                {this.state.error.toString()}
                <br />
                {this.state.errorInfo?.componentStack}
              </pre>
            </details>
          )}
          <button
            onClick={() => {
              this.setState({ hasError: false, error: null, errorInfo: null });
              this.props?.router?.push("/dashboard/content");
            }}
            className="mt-6 px-4 py-2 bg-red-600 text-white rounded-md hover:bg-red-700 focus:outline-none focus:ring-2 focus:ring-red-500 focus:ring-offset-2"
          >
            Zurück zur letzten funktionierenden Ansicht
          </button>
        </div>
      );
    }

    return this.props.children;
  }
}

export default ErrorBoundary;
