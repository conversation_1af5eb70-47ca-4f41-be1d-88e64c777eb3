"use client";

import { useSearch } from "@/lib/contexts/search-context";
import { FaSearch } from "react-icons/fa";
export const Searchbar = () => {
  const { searchTerm, setSearchTerm } = useSearch();

  return (
    <div
      className={
        "p-2 border border-gray-300 h-12 rounded-md flex w-1/5 gap-2 focus-within:border focus-within:border-amber-400"
      }
    >
      <FaSearch />
      <input
        type="text"
        value={searchTerm}
        onChange={(e) => setSearchTerm(e.target.value)}
      />
    </div>
  );
};
