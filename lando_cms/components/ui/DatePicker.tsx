import { LocalizationProvider } from "@mui/x-date-pickers";
import { AdapterDayjs } from "@mui/x-date-pickers/AdapterDayjs";
import { DatePicker } from "@mui/x-date-pickers/DatePicker";
import { PickerValue } from "@mui/x-date-pickers/internals";
import dayjs from "dayjs";
import { Dispatch, SetStateAction } from "react";

interface DatePickerProps {
  value?: string;
  setValue: Dispatch<SetStateAction<PickerValue>>;
}

export default function BasicDatePicker({ setValue }: DatePickerProps) {
  return (
    <LocalizationProvider dateAdapter={AdapterDayjs}>
      <DatePicker
        label="Controlled picker"
        value={dayjs("2022-04-17")}
        onChange={(newValue) => setValue(newValue)}
      />
    </LocalizationProvider>
  );
}
