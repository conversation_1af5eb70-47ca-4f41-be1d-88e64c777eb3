interface InputProps {
  type?: string;
  placeholder: string;
  value?: string;
  onChange: (e: string) => void;
}

export default function Input({
  type = "text",
  placeholder = "Hier eingeben..",
  value,
  onChange,
}: InputProps) {
  return (
    <input
      className={
        "p-2 border border-gray-300 rounded-md focus:outline-0 focus:border-2 focus:border-amber-300 "
      }
      type={type}
      placeholder={placeholder}
      value={value}
      onChange={(e) => onChange(e.target.value)}
    />
  );
}
