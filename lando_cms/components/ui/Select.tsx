/* eslint-disable @typescript-eslint/no-explicit-any */
import Box from "@mui/material/Box"; // Importiere Box für Styling-Container
import FormControl from "@mui/material/FormControl";
import InputLabel from "@mui/material/InputLabel";
import MenuItem from "@mui/material/MenuItem";
import Select from "@mui/material/Select";
import { Dispatch, SetStateAction } from "react";

// Definiere ein Interface für deine Dropdown-Optionen
interface SelectOption {
  value: string | number; // Wert, der gespeichert wird (kann string oder number sein)
  label: string; // Text, der angezeigt wird
}

// Beispiel-Daten für deine Optionen
const ageOptions: SelectOption[] = [
  { value: 10, label: "Ten" },
  { value: 20, label: "Twenty" },
  { value: 30, label: "Thirty" },
  { value: 40, label: "Forty" },
  { value: 50, label: "Fifty" },
];

type SelectProps = {
  value: string;
  handleChange: Dispatch<SetStateAction<string>> | any;
};

export default function SelectLabels({ value, handleChange }: SelectProps) {
  return (
    <Box>
      {" "}
      {/* Verwende Box als Container für besseres Layout */}
      {/* Beispiel 1: Mit Label und Helper-Text */}
      <FormControl sx={{ width: "100%" }}>
        <InputLabel id="age-select-label">Age</InputLabel>
        <Select
          labelId="age-select-label"
          id="age-select-helper"
          value={value}
          label="Age"
          onChange={handleChange}
        >
          {/* Option für "None" oder Platzhalter */}
          <MenuItem value="">
            <em>None</em>
          </MenuItem>
          {/* Dynamisches Rendern der MenuItems */}
          {ageOptions.map((option) => (
            <MenuItem key={option.value} value={option.value}>
              {option.label}
            </MenuItem>
          ))}
        </Select>
      </FormControl>
      {/* Beispiel 2: Ohne Label, mit displayEmpty */}
    </Box>
  );
}
