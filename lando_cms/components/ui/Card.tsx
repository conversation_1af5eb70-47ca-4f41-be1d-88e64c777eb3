import { useRouter } from "next/router";
import { type HTMLAttributes, forwardRef } from "react";
import { FaChevronLeft } from "react-icons/fa";

export const Card = forwardRef<HTMLDivElement, HTMLAttributes<HTMLDivElement>>(
  ({ className = "", children, ...props }, ref) => {
    const router = useRouter();

    return (
      <div
        ref={ref}
        className={`rounded-lg m-5 bg-white  ${className}`}
        {...props}
      >
        <div className=" flex flex-col gap-y-4">
          <button
            onClick={() => router.back()}
            className="flex gap-x-2 w-min px-3 items-center text-xl mt-4 text-red-600 hover:cursor-pointer hover:text-gray-300 font-semibold"
          >
            <FaChevronLeft />
            Zurück
          </button>
          {children}
        </div>
      </div>
    );
  }
);

export const CardWithoutBack = forwardRef<
  HTMLDivElement,
  HTMLAttributes<HTMLDivElement>
>(({ className = "", children, ...props }, ref) => {
  return (
    <div
      ref={ref}
      className={`rounded-lg border border-gray-200 bg-white shadow-sm ${className}`}
      {...props}
    >
      {children}
    </div>
  );
});

export const CardHeader = forwardRef<
  HTMLDivElement,
  HTMLAttributes<HTMLDivElement>
>(({ className = "", children, ...props }, ref) => (
  <div
    ref={ref}
    className={`flex flex-col space-y-1.5 p-6 ${className}`}
    {...props}
  >
    {children}
  </div>
));

export const CardTitle = forwardRef<
  HTMLHeadingElement,
  HTMLAttributes<HTMLHeadingElement>
>(({ className = "", children, ...props }, ref) => (
  <h3
    ref={ref}
    className={`text-2xl font-semibold leading-none tracking-tight ${className}`}
    {...props}
  >
    {children}
  </h3>
));

export const CardDescription = forwardRef<
  HTMLParagraphElement,
  HTMLAttributes<HTMLParagraphElement>
>(({ className = "", children, ...props }, ref) => (
  <p ref={ref} className={`text-sm text-gray-600 ${className}`} {...props}>
    {children}
  </p>
));

export const CardContent = forwardRef<
  HTMLDivElement,
  HTMLAttributes<HTMLDivElement>
>(({ className = "", children, ...props }, ref) => (
  <div ref={ref} className={`p-6 pt-0 ${className}`} {...props}>
    {children}
  </div>
));

Card.displayName = "Card";
CardWithoutBack.displayName = "CardWithoutBack";
CardHeader.displayName = "CardHeader";
CardTitle.displayName = "CardTitle";
CardDescription.displayName = "CardDescription";
CardContent.displayName = "CardContent";
