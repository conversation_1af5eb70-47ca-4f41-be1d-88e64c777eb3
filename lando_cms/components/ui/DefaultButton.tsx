import React from "react";
import Spinner from "./Spinner";

export enum ButtonType {
  PRIMARY = "primary",
  SECONDARY = "secondary",
  DESTRUCTIVE = "destructive",
}

interface DefaultButtonProps {
  text: string;
  onClick: () => void;
  type: ButtonType;
  classname?: string;
  isLoading?: boolean;
}

export default function DefaultButton({
  text,
  onClick,
  type,
  classname,
  isLoading = false,
}: DefaultButtonProps) {
  const getButtonType = () => {
    switch (type) {
      case ButtonType.PRIMARY:
        return (classname = "bg-[#277A6E] text-white");
      case ButtonType.SECONDARY:
        return "bg-amber-400";

      default:
        return "bg-green-950";
    }
  };

  return (
    <button
      className={`${getButtonType()} ${classname} p-3 text-[16px] rounded-md min-w-[150px] hover:scale-105 hover:cursor-pointer`}
      onClick={onClick}
      disabled={isLoading}
    >
      {isLoading ? <Spinner /> : text}
    </button>
  );
}
