import Link from "next/link";
import React from "react";

interface SideBarItemProps {
  href: string;
  icon: React.ReactNode;
  label: string;
  isActive?: boolean;
}

export const SideBarItem = ({
  href,
  icon,
  label,
  isActive,
}: SideBarItemProps) => {
  return (
    <Link
      href={href}
      className={` flex py-1 px-4 mb-6 items-center gap-x-7 rounded-md transition duration-200 ease-in-out  text-lg font-semibold  w-full justify-start hover:bg-gray-400 ${
        isActive ? " text-[#339988]" : "text-gray-300"
      }`}
    >
      {icon}
      <span>{label}</span>
    </Link>
  );
};
