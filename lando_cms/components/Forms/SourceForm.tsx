import {
  Card,
  CardContent,
  CardDescription,
  CardHeader,
  CardTitle,
} from "../ui/Card";
import BasicDatePicker from "../ui/DatePicker";
import DefaultButton, { ButtonType } from "../ui/DefaultButton";
import Input from "../ui/Input";
import { Label } from "../ui/Label";
import SelectLabels from "../ui/Select";
import { Textarea } from "../ui/TextArea";

export function SourceForm() {
  return (
    <Card>
      <CardHeader>
        <CardTitle>Neue Quelle anlegen</CardTitle>
        <CardDescription>Fülle alle Felder aus</CardDescription>
      </CardHeader>
      <CardContent className="space-y-4">
        <div className="flex flex-col">
          <Label>Dokumenten Titel</Label>
          <Input
            onChange={() => {}}
            value=""
            placeholder="Environment Production"
          />
        </div>
        <div className="grid grid-cols-2 gap-4">
          <div className="flex flex-col space-y-2">
            <Label>Dokumententyp</Label>
            <SelectLabels value={"10"} handleChange={() => {}} />
          </div>
          <div className="flex flex-col space-y-2">
            <Label>Quellentyp</Label>
            <SelectLabels value={"10"} handleChange={() => {}} />
          </div>
        </div>
        <div className="flex flex-col space-y-2">
          <Label>Environment Name</Label>
          <Textarea />
        </div>
        <div className="space-y-4 flex flex-col">
          <Label>Ablaufdatum</Label>
          <BasicDatePicker value={""} setValue={() => {}} />
        </div>

        {/** Hier Colorpicker */}
        <DefaultButton
          type={ButtonType.PRIMARY}
          onClick={() => {}}
          classname="w-full"
          text="Erstellen"
        />
      </CardContent>
    </Card>
  );
}
