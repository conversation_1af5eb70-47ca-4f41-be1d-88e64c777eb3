import {
  Card,
  CardContent,
  CardDescription,
  Card<PERSON>eader,
  CardTitle,
} from "../ui/Card";
import DefaultButton, { ButtonType } from "../ui/DefaultButton";
import Input from "../ui/Input";
import { Label } from "../ui/Label";

export function EnterpriseForm() {
  return (
    <Card>
      <CardHeader>
        <CardTitle>Produkt erstellen</CardTitle>
        <CardDescription>Fügen Sie ein neues Produkt hinzu</CardDescription>
      </CardHeader>
      <CardContent className="space-y-4">
        <div className="flex flex-col">
          <Label>Kundenname</Label>
          <Input onChange={() => {}} value="" placeholder="Kunden eingeben" />
        </div>
        <div className="flex flex-col">
          <Label>Link</Label>
          <Input onChange={() => {}} value="" placeholder="Link eingeben" />
        </div>
        <div className="grid grid-cols-2 gap-4">
          <div className="flex flex-col">
            <Label>Name des Ansprechpartners (€)</Label>
            <Input onChange={() => {}} value="" placeholder="Max Mustermann" />
          </div>
          <div className="flex flex-col">
            <Label>Email</Label>
            <Input
              onChange={() => {}}
              value=""
              placeholder="<EMAIL>"
            />
          </div>
        </div>
        <div className="space-y-2">
          <Label>Betreuer</Label>
          {/* TODO: Selection mit Usern */}
        </div>
        <DefaultButton
          type={ButtonType.PRIMARY}
          onClick={() => {}}
          classname="w-full"
          text="Erstellen"
        />
      </CardContent>
    </Card>
  );
}
