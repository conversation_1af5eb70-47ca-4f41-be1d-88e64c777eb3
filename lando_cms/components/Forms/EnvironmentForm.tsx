import {
  Card,
  CardContent,
  CardDescription,
  Card<PERSON>ead<PERSON>,
  CardTitle,
} from "../ui/Card";
import DefaultButton, { ButtonType } from "../ui/DefaultButton";
import Input from "../ui/Input";
import { Label } from "../ui/Label";
import SelectLabels from "../ui/Select";

export function EnvironmentForm() {
  return (
    <Card>
      <CardHeader>
        <CardTitle>Neues Environment anlegen</CardTitle>
        <CardDescription>Fülle alle Felder aus</CardDescription>
      </CardHeader>
      <CardContent className="space-y-4">
        <div className="flex flex-col space-y-2">
          <Label>Environment Name</Label>
          <Input
            onChange={() => {}}
            value=""
            placeholder="Environment Production"
          />
        </div>
        <div className="flex flex-col space-y-2">
          <Label>Beschreibung</Label>
          <Input onChange={() => {}} value="" placeholder="Hier eingeben" />
        </div>
        <div className="flex flex-col space-y-2">
          <Label>EntryPoint Id</Label>
          <Input onChange={() => {}} value="" placeholder="Hier eingeben" />
        </div>
        <div className="space-y-2">
          <Label>Sprache</Label>
          <SelectLabels value={"10"} handleChange={() => {}} />
        </div>
        {/** Hier Colorpicker */}
        <DefaultButton
          type={ButtonType.PRIMARY}
          onClick={() => {}}
          classname="w-full"
          text="Erstellen"
        />
      </CardContent>
    </Card>
  );
}
