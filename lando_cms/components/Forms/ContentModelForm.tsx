import DnDCardsBox from "../DnDCardsBox";
import {
  Card,
  CardContent,
  CardDescription,
  CardHeader,
  CardTitle,
} from "../ui/Card";
import DefaultButton, { ButtonType } from "../ui/DefaultButton";
import Input from "../ui/Input";
import { Label } from "../ui/Label";

export function ContentModelForm() {
  return (
    <Card>
      <CardHeader>
        <CardTitle>Exportieren</CardTitle>
        <CardDescription>Fülle alle Felder aus</CardDescription>
      </CardHeader>
      <CardContent className="space-y-6">
        <div className="flex flex-col space-y-2">
          <Label>Titel</Label>
          <Input onChange={() => {}} value="" placeholder="Hier eintippen" />
        </div>

        <div className="flex flex-col space-y-2">
          <Label>Beschreibung</Label>
          <Input onChange={() => {}} value="" placeholder="Hier beschreiben" />
        </div>

        <div>
          <button className="w-full p-3 bg-amber-100 hover:bg-amber-50 hover:cursor-pointer font-semibold">
            HINZUFÜGEN
          </button>
        </div>

        <div className="w-full">
          <DnDCardsBox />
        </div>

        {/** Hier Colorpicker */}
        <DefaultButton
          type={ButtonType.PRIMARY}
          onClick={() => {}}
          classname="w-full"
          text="Erstellen"
        />
      </CardContent>
    </Card>
  );
}
