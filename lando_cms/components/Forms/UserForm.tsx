import { useState } from "react";
import {
  Card,
  CardContent,
  CardDescription,
  CardHeader,
  CardTitle,
} from "../ui/Card";
import DefaultButton, { ButtonType } from "../ui/DefaultButton";
import Input from "../ui/Input";
import { Label } from "../ui/Label";

export function UserForm() {
  const [newUser, setNewUser] = useState({
    first_name: "",
    last_name: "",
    email: "",
    department: "",
    user_type: "",
  });

  const handleCreateUser = () => {};

  return (
    <Card>
      <CardHeader>
        <CardTitle>Benutzer erstellen</CardTitle>
        <CardDescription>Erstellen Sie einen neuen <PERSON>utzer</CardDescription>
      </CardHeader>
      <CardContent className="space-y-4">
        <div className="grid grid-cols-2 gap-4">
          <div className="flex flex-col">
            <Label>Vorname</Label>
            <Input
              placeholder="Max"
              type="text"
              value=""
              onChange={(value) => {
                setNewUser({ ...newUser, first_name: value });
              }}
            />
          </div>
          <div className="flex flex-col">
            <Label>Nachname</Label>
            <Input
              placeholder="Mustermann"
              type="text"
              value=""
              onChange={(value) => {
                setNewUser({ ...newUser, last_name: value });
              }}
            />
          </div>
        </div>
        <div className="grid grid-cols-3 gap-4">
          <div className="flex flex-col">
            <Label>E-Mail</Label>
            <Input
              type="text"
              value=""
              onChange={(value) => {
                setNewUser({ ...newUser, email: value });
              }}
              placeholder="<EMAIL>"
            />
          </div>

          <div className="flex flex-col">
            <Label>Abteilung</Label>
            <Input
              type="text"
              value=""
              onChange={(value) => {
                setNewUser({ ...newUser, department: value });
              }}
              placeholder="Entwicklung"
            />
          </div>
        </div>
        <div className="flex flex-col">
          <Label>User Type</Label>
          <Input
            type="text"
            value=""
            onChange={(value) => {
              setNewUser({ ...newUser, user_type: value });
            }}
            placeholder="Frontend"
          />
        </div>

        <DefaultButton
          type={ButtonType.PRIMARY}
          onClick={() => {}}
          classname="w-full"
          text="Erstellen"
        />
      </CardContent>
    </Card>
  );
}
