import {
  Card,
  CardContent,
  CardDescription,
  Card<PERSON>eader,
  CardTitle,
} from "../ui/Card";
import DefaultButton, { ButtonType } from "../ui/DefaultButton";
import { Label } from "../ui/Label";
import SelectLabels from "../ui/Select";
import { Textarea } from "../ui/TextArea";

export function ExportForm() {
  return (
    <Card>
      <CardHeader>
        <CardTitle>Exportieren</CardTitle>
        <CardDescription>Fülle alle Felder aus</CardDescription>
      </CardHeader>
      <CardContent className="space-y-8">
        <div className="flex flex-col space-y-2">
          <Label>Exportieren für</Label>
          <SelectLabels value={"10"} handleChange={() => {}} />
        </div>
        <div className="grid grid-cols-3 gap-4">
          <div className="flex flex-col space-y-2">
            <Label>Export Ursprung</Label>
            <SelectLabels value={"10"} handleChange={() => {}} />
          </div>
          <div className="flex flex-col space-y-2">
            <Label>Entrypoint</Label>
            <SelectLabels value={"10"} handleChange={() => {}} />
          </div>
          <div className="flex flex-col space-y-2">
            <Label>Export Target</Label>
            <SelectLabels value={"10"} handleChange={() => {}} />
          </div>
        </div>
        <div className="grid grid-cols-2 gap-4">
          <div className="flex flex-col space-y-2">
            <Label>Level</Label>
            <SelectLabels value={"10"} handleChange={() => {}} />
          </div>
          <div className="flex flex-col space-y-2">
            <Label>Status</Label>
            <SelectLabels value={"10"} handleChange={() => {}} />
          </div>
        </div>

        <div className="flex flex-col space-y-2">
          <Label>Kommentar</Label>
          <Textarea />
        </div>

        <div className="flex flex-col space-y-2">
          <Label>Sprache</Label>
          <SelectLabels value={"10"} handleChange={() => {}} />
        </div>
        {/** Hier Colorpicker */}
        <DefaultButton
          type={ButtonType.PRIMARY}
          onClick={() => {}}
          classname="w-full"
          text="Erstellen"
        />
      </CardContent>
    </Card>
  );
}
