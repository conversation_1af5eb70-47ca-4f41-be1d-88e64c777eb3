import formatTableData from "@/lib/functions/format-table-data"; // Benötigt für Datenformatierung
import { useRouter } from "next/router"; // Wird hier verwendet, um dataType zu bekommen
import React, { useMemo } from "react";

import EnvironmentSelector from "@/components/EnvironmentSelector";
import { LoadingTable } from "@/components/LoadingTable";
import { DynamicTable } from "@/components/Table";
import DefaultButton, { ButtonType } from "@/components/ui/DefaultButton";
import { PageWrapper } from "@/components/wrapper/PageWrapper";

interface DashboardTablePageProps {
  data: unknown[];
  head: string[];
  dataType: string;
  isLoading?: boolean;
  error?: string;
  searchTerm: string;
}

const DashboardTablePage: React.FC<DashboardTablePageProps> = ({
  data,
  head,
  dataType,
  isLoading = false,
  error,
  searchTerm = "",
}) => {
  const router = useRouter();

  const formattedTableContent = useMemo(() => {
    if (!data) return [];

    const tableData = formatTableData(data, dataType);

    if (searchTerm?.length < 3) return tableData;

    const searchedTableData = tableData?.filter((d) =>
      d.title?.includes(searchTerm)
    );

    return searchedTableData;
  }, [data, dataType, searchTerm]);

  // Fehlerbehandlung
  if (error) {
    return (
      <PageWrapper
        rowOneContent={
          <h3 className="text-3xl font-semibold text-red-500">
            Fehler beim Laden der {dataType}
          </h3>
        }
        rowTwoContent={<p>{error}</p>}
      />
    );
  }

  // Ladezustand anzeigen (z.B. wenn router.isFallback true ist)
  if (isLoading || (!data && !head)) {
    return <LoadingTable />;
  }

  return (
    <PageWrapper
      rowOneContent={
        <div
          style={{
            display: "flex",
            width: "100%",
            alignItems: "center",
            justifyContent: "space-between",
          }}
        >
          <h3 className="text-3xl font-semibold text-gray-500">
            {dataType
              ? dataType.charAt(0).toUpperCase() + dataType.slice(1)
              : "Daten"}
          </h3>

          <div
            style={{
              display: "flex",
              columnGap: "10px",
              alignItems: "center",
              justifyContent: "space-between",
            }}
          >
            <EnvironmentSelector />
            <DefaultButton
              text={`Neues ${dataType}`}
              type={ButtonType.PRIMARY}
              onClick={() => {
                router.push(`/dashboard/${dataType}/create`);
              }}
            />
          </div>
        </div>
      }
      rowTwoContent={
        <DynamicTable
          clickRow={() => {
            router.push(`/dashboard/${dataType}/edit`);
          }}
          rows={formattedTableContent || []}
          object={data}
          headers={head}
        />
      }
    />
  );
};

export default DashboardTablePage;
