"use client";

import { useState } from "react";

import { Reorder } from "framer-motion";
import { GripVertical } from "lucide-react";
import { CardHeader, CardTitle, CardWithoutBack } from "./ui/Card";

interface CardItem {
  id: string;
  title: string;
  description: string;
  content: string;
}

export default function DnDCardsBox() {
  const [items, setItems] = useState<CardItem[]>([
    {
      id: "1",
      title: "Erste Karte",
      description: "Dies ist die erste Karte in der Liste",
      content:
        "Hier steht der Inhalt der ersten Karte. Sie können diese Karte nach oben oder unten ziehen.",
    },
    {
      id: "2",
      title: "Zweite Karte",
      description: "Eine weitere Karte zum Sortieren",
      content:
        "Der Inhalt der zweiten Karte. Probieren Sie das Drag-and-Drop aus!",
    },
    {
      id: "3",
      title: "Dritte Karte",
      description: "Noch eine sortierbare Karte",
      content:
        "Diese Ka<PERSON> kann ebenfalls verschoben werden. <PERSON><PERSON><PERSON>e sie an eine neue Position.",
    },
    {
      id: "4",
      title: "Vierte Karte",
      description: "Die letzte Karte in der Liste",
      content:
        "Auch diese Karte lässt sich per Drag-and-Drop verschieben und neu anordnen.",
    },
  ]);

  return (
    <div className="w-full space-y-4">
      <div className="text-start mb-2">
        <h1 className="text-2xl text-gray-500 font-bold mb-2">Fields</h1>
      </div>

      <Reorder.Group
        axis="y"
        values={items}
        onReorder={setItems}
        className="space-y-4"
      >
        {items.map((item) => (
          <Reorder.Item
            key={item.id}
            value={item}
            className="cursor-grab active:cursor-grabbing"
          >
            <CardWithoutBack className="transition-all duration-200 hover:shadow-lg active:shadow-xl border-2 hover:border-primary/20">
              <CardHeader className="pb-3">
                <div className="flex items-center gap-3">
                  <GripVertical className="h-5 w-5 text-muted-foreground" />
                  <div className="flex-1">
                    <CardTitle className="text-lg">{item.title}</CardTitle>
                    <p className="text-sm text-muted-foreground mt-1">
                      {item.description}
                    </p>
                  </div>
                </div>
              </CardHeader>
            </CardWithoutBack>
          </Reorder.Item>
        ))}
      </Reorder.Group>
    </div>
  );
}
