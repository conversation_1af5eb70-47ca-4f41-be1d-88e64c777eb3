import React from "react";

interface PageWrapperProps {
  rowOneContent: React.ReactNode;
  rowTwoContent: React.ReactNode;
}

export const PageWrapper = ({
  rowOneContent,
  rowTwoContent,
}: PageWrapperProps) => {
  return (
    <div className=" grid grid-rows-[10%_90%]">
      <div className="p-[30px] ">{rowOneContent}</div>
      <div className="p-[30px]">{rowTwoContent}</div>
    </div>
  );
};
