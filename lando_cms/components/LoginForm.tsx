import { queryClient } from "@/lib/api/api-client";
import { loginUser } from "@/lib/api/user-api";
import { useRouter } from "next/router";
import { useState } from "react";
import DefaultButton, { ButtonType } from "./ui/DefaultButton";
import Input from "./ui/Input";

export default function LoginForm() {
  const [email, setEmail] = useState("");
  const [password, setPassword] = useState("");
  const [loading, setLoad] = useState(false);

  const router = useRouter();

  const handleLogin = async () => {
    try {
      setLoad(true);
      queryClient.fetchQuery({
        queryKey: ["user"],
        queryFn: async () => {
          const user = await loginUser(email, password);

          if (user) {
            router.push("/dashboard/content");
          }
          return user;
        },
      });
    } catch {
      console.error("Login failed");
    } finally {
      setLoad(false);
    }

    //router.push("/dashboard/content");
  };

  return (
    <div style={{ display: "flex", flexDirection: "column" }}>
      <div
        style={{ display: "flex", flexDirection: "column", marginBottom: 20 }}
        className="gap-y-4"
      >
        <Input
          type="email"
          placeholder="Email"
          value={email}
          onChange={setEmail}
        />
        <Input
          type="password"
          placeholder="Password"
          value={password}
          onChange={setPassword}
        />
      </div>

      <div style={{ marginTop: "16px" }} className="gap-x-4 flex ">
        <DefaultButton
          text="Login"
          onClick={handleLogin}
          type={ButtonType.PRIMARY}
          isLoading={loading}
        />
        <DefaultButton
          text="Forgot Password?"
          onClick={() => {
            router.push("/dashboard/content");
          }}
          type={ButtonType.SECONDARY}
        />
      </div>
    </div>
  );
}
