import { useRouter } from "next/router";

export const Profile = () => {
  const router = useRouter();

  return (
    <div
      className={
        "flex justify-around items-center rounded-md p-1 px-2 bg-amber-300 max-w-[300px]"
      }
      onClick={() => router.push("/settings")}
    >
      <div>
        <p><PERSON></p>
      </div>
      <div
        className={
          "flex justify-center items-center bg-white font-bold rounded-full p-2"
        }
      >
        SR
      </div>
    </div>
  );
};
