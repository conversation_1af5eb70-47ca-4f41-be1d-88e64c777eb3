/* eslint-disable @typescript-eslint/no-explicit-any */
"use client";
import React, { memo, useState, useEffect } from "react";
import Paper from "@mui/material/Paper";
import Table from "@mui/material/Table";
import TableBody from "@mui/material/TableBody";
import TableCell from "@mui/material/TableCell";
import TableContainer from "@mui/material/TableContainer";
import TableHead from "@mui/material/TableHead";
import TablePagination from "@mui/material/TablePagination";
import TableRow from "@mui/material/TableRow";
import Checkbox from "@mui/material/Checkbox";
import {
  IconButton,
  Toolbar,
  Tooltip,
  Typography,
  Box,
  Collapse,
} from "@mui/material";
import { alpha } from "@mui/material/styles";

import { FilterIcon } from "lucide-react";
import { MdDeleteForever } from "react-icons/md";
import { FaChevronDown, FaChevronUp } from "react-icons/fa"; // This line was already correct but if you had other imports, ensure they are right too.

interface RowData {
  id: string | number;
  [key: string]: any;
  details?: React.ReactNode; // Optional property for collapsible content
}

interface EnhancedTableToolbarProps {
  numSelected: number;
  tableName?: string;
  onDeleteSelected?: () => void;
  onFilterClick?: () => void;
}

function EnhancedTableToolbar(props: EnhancedTableToolbarProps) {
  const {
    numSelected,
    tableName = "Table",
    onDeleteSelected,
    onFilterClick,
  } = props;

  return (
    <Toolbar
      sx={[
        {
          pl: { sm: 2 },
          pr: { xs: 1, sm: 1 },
        },
        numSelected > 0 && {
          bgcolor: (theme) =>
            alpha(
              theme.palette.primary.main,
              theme.palette.action.activatedOpacity
            ),
        },
      ]}
    >
      {numSelected > 0 ? (
        <Typography
          sx={{ flex: "1 1 100%" }}
          color="inherit"
          variant="subtitle1"
          component="div"
        >
          {numSelected} selected
        </Typography>
      ) : (
        <Typography
          sx={{ flex: "1 1 100%" }}
          variant="h6"
          id="tableTitle"
          component="div"
        >
          {tableName}
        </Typography>
      )}
      {numSelected > 0 ? (
        <Tooltip title="Delete">
          <IconButton onClick={onDeleteSelected}>
            <MdDeleteForever color="red" />
          </IconButton>
        </Tooltip>
      ) : (
        <Tooltip title="Filter list">
          <IconButton onClick={onFilterClick}>
            <FilterIcon color="#339988" />
          </IconButton>
        </Tooltip>
      )}
    </Toolbar>
  );
}

// --- Collapsible Row Component ---
function Row({
  row,
  headers,
  clickRow,
  isSelected,
  handleRowCheckboxClick,
  objectRow, // Pass the original object for _id
}: {
  row: RowData;
  headers: string[];
  clickRow: (rowId?: string | number) => void;
  isSelected: (id: string | number) => boolean;
  handleRowCheckboxClick: (event: any, id: string | number) => void;
  objectRow: any; // The full original object from your `object` prop
}) {
  const [open, setOpen] = useState(false);
  const itemSelected = isSelected(objectRow._id);

  return (
    <React.Fragment>
      <TableRow
        hover
        role="checkbox"
        aria-checked={itemSelected}
        tabIndex={-1}
        key={objectRow._id}
        selected={itemSelected}
        sx={{ "& > *": { borderBottom: "unset" } }} // Important for collapsing
      >
        <TableCell padding="checkbox">
          <Checkbox
            color="primary"
            checked={itemSelected}
            inputProps={{
              "aria-labelledby": `enhanced-table-checkbox-${objectRow._id}`,
            }}
            onClick={(event) => handleRowCheckboxClick(event, objectRow._id)}
          />
        </TableCell>
        <TableCell>
          {/* REMOVED WHITESPACE HERE */}

          <IconButton
            aria-label="expand row"
            size="small"
            onClick={(e) => {
              e.stopPropagation(); // Prevent row click from firing
              setOpen(!open);
            }}
          >
            {open ? (
              <FaChevronUp color="#339988" />
            ) : (
              <FaChevronDown color="#339988" />
            )}
          </IconButton>
        </TableCell>
        {Object.keys(row).map((key) => {
          if (key === "id" || key === "details") {
            return null; // Skip rendering 'id' and 'details' columns
          }
          const columnValue = row[key];
          return (
            <TableCell // Ensure no whitespace between this and previous closing TableCell
              sx={{ color: "gray", fontWeight: "semibold" }}
              key={`${objectRow._id}-${key}`}
              align={"left"}
              onClick={() => clickRow()} // Still allow main row click
            >
              {!columnValue ? "No Results" : columnValue}
            </TableCell>
          );
        })}
      </TableRow>
      {open && (
        <TableRow>
          <TableCell
            style={{ paddingBottom: 0, paddingTop: 0 }}
            colSpan={headers.length + 2}
          >
            {/* REMOVED WHITESPACE HERE */}
            <Collapse in={open} timeout="auto" unmountOnExit>
              <Box sx={{ margin: 1 }}>
                <div>Hier kommen die Details hin</div>
              </Box>
            </Collapse>
          </TableCell>
        </TableRow>
      )}
    </React.Fragment>
  );
}

// --- DynamicTable Component ---
export const DynamicTable = memo(function DynamicTable({
  headers,
  rows,
  clickRow,
  tableName = "",
  onDeleteSelected,
  onFilterClick,
  object,
}: {
  headers: string[];
  rows: RowData[];
  clickRow: (rowId?: string | number) => void;
  tableName?: string;
  onDeleteSelected?: (selectedIds: Array<string | number>) => void;
  onFilterClick?: () => void;
  object: any[]; // 'object' is an array now, to align with `rows` by index
}) {
  const [page, setPage] = useState(0);
  const [rowsPerPage, setRowsPerPage] = useState(25);
  const [selectedIds, setSelectedIds] = useState<Array<string | number>>([]);

  const handleChangePage = (event: unknown, newPage: number) => {
    setPage(newPage);
  };

  const handleChangeRowsPerPage = (
    event: React.ChangeEvent<HTMLInputElement>
  ) => {
    setRowsPerPage(+event.target.value);
    setPage(0);
  };

  const handleSelectAllClick = (event: React.ChangeEvent<HTMLInputElement>) => {
    if (event.target.checked) {
      // Use the IDs from the `object` prop for selection
      const newSelectedIds = object.map((o) => o._id);
      setSelectedIds(newSelectedIds);
    } else {
      setSelectedIds([]);
    }
  };

  const handleRowCheckboxClick = (
    event: React.MouseEvent<HTMLInputElement>,
    id: string | number
  ) => {
    event.stopPropagation();
    const selectedIndex = selectedIds.indexOf(id);
    let newSelected: Array<string | number> = [];

    if (selectedIndex === -1) {
      newSelected = [...selectedIds, id];
    } else {
      newSelected = selectedIds.filter((selectedId) => selectedId !== id);
    }
    console.log(`Toggling ID: ${id}. New selectedIds:`, newSelected); // Debugging log
    setSelectedIds(newSelected);
  };

  const isSelected = (id: string | number) => selectedIds.indexOf(id) !== -1;

  const numSelected = selectedIds.length;
  const rowCount = object.length; // Use object.length for total rows to select

  const handleDelete = () => {
    if (onDeleteSelected) {
      onDeleteSelected(selectedIds);
      setSelectedIds([]); // Clear selection after deletion
    }
  };

  return (
    <Paper
      sx={{
        display: "flex",
        flexDirection: "column",
        width: "100%",
        overflow: "hidden",
        height: "850px",
      }}
    >
      <EnhancedTableToolbar
        numSelected={numSelected}
        tableName={tableName}
        onDeleteSelected={handleDelete}
        onFilterClick={onFilterClick}
      />
      <TableContainer sx={{ height: "95%" }}>
        <Table
          stickyHeader
          aria-label="sticky table"
          sx={{ overflow: "scroll" }}
        >
          <TableHead>
            <TableRow>
              <TableCell padding="checkbox">
                <Checkbox
                  color="primary"
                  indeterminate={numSelected > 0 && numSelected < rowCount}
                  checked={rowCount > 0 && numSelected === rowCount}
                  onChange={handleSelectAllClick}
                  inputProps={{
                    "aria-label": "select all desserts",
                  }}
                />
              </TableCell>
              <TableCell />
              {/* REMOVED WHITESPACE HERE */}
              {headers.map((column, index) => (
                <TableCell
                  sx={{
                    backgroundColor: "#f5f5f5",
                    fontSize: 16,
                    fontWeight: "bold",
                    color: "gray",
                  }}
                  key={index}
                  align={"left"}
                  style={{ minWidth: 170 }}
                >
                  {column}
                </TableCell>
              ))}
            </TableRow>
          </TableHead>
          <TableBody>
            {rows
              .slice(page * rowsPerPage, page * rowsPerPage + rowsPerPage)
              .map((row: RowData, index: number) => {
                const objectOfRow = object[page * rowsPerPage + index]; // Get the correct object from the `object` array
                return (
                  <Row
                    key={objectOfRow._id} // Use the actual ID from the object for the key
                    row={row}
                    headers={headers}
                    clickRow={() => clickRow()}
                    isSelected={isSelected}
                    handleRowCheckboxClick={handleRowCheckboxClick}
                    objectRow={objectOfRow}
                  />
                );
              })}
          </TableBody>
        </Table>
      </TableContainer>
      <TablePagination
        rowsPerPageOptions={[10, 25, 100]}
        component="div"
        count={rows.length}
        rowsPerPage={rowsPerPage}
        page={page}
        onPageChange={handleChangePage}
        onRowsPerPageChange={handleChangeRowsPerPage}
      />
    </Paper>
  );
});
