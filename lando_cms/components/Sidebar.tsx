import React from "react";
import { SideBarItem } from "./SidebarItem";
import { usePathname } from "next/navigation";
interface SidebarProps {
  menuItems: {
    href: string;
    icon: React.ReactNode;
    label: string;
  }[];
}

export const SidebarNav = ({ menuItems }: SidebarProps) => {
  const pathname = usePathname();
  return (
    <ul style={{ padding: "20px" }}>
      {menuItems.map((item, index) => (
        <SideBarItem key={index} {...item} isActive={pathname === item.href} />
      ))}
    </ul>
  );
};
